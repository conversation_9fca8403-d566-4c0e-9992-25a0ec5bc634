// src/pages/home/<USER>
export interface PolygonData {
  id: string;
  name: string;
  path: BMapGL.Point[];
  properties: any;
}

export interface FanShapedParams {
  startRatio: number;
  endRatio: number;
  isSelected: boolean;
  isHovered: boolean;
  k?: number;
  h: number;
}


/**
 * 将坐标数组转换为 BMapGL.Point 数组
 * @param coordinates [lng, lat][] 坐标数组
 * @returns BMapGL.Point[]
 */
function convertCoordinatesToPoints(coordinates: number[][]): BMapGL.Point[] {
  return coordinates.map(([lng, lat]) => new BMapGL.Point(lng, lat));
}

/**
 * 根据区域属性获取多边形样式
 * @param properties 区域属性
 * @returns 样式对象
 */
export function getPolygonStyle(properties: any) {
  const colors = ['#384D68', '#4A5D7A', '#5C6E8C', '#6E7F9E', '#8090B0'];
  const colorIndex = (properties.join || 0) % colors.length;

  return {
    strokeColor: "#FFFFFF",
    strokeWeight: 1,
    fillColor: colors[colorIndex],
    fillOpacity: 0.6
  };
}

/**
 * 计算多边形的中心点（质心）
 * @param points BMapGL.Point[] 多边形的点数组
 * @returns BMapGL.Point 中心点
 */
export function calculatePolygonCenter(points: BMapGL.Point[]): BMapGL.Point {
  if (points.length === 0) {
    return new BMapGL.Point(0, 0);
  }

  let totalLng = 0;
  let totalLat = 0;

  points.forEach(point => {
    totalLng += point.lng;
    totalLat += point.lat;
  });

  return new BMapGL.Point(
    totalLng / points.length,
    totalLat / points.length
  );
}

/**
 * 将 GeoJSON FeatureCollection 转换为 Polygon 组件所需的数据格式
 * @param featureCollection GeoJSON FeatureCollection
 * @param isOutline 是不是轮廓coordinates取第一个就可以
 * @returns PolygonData[]
 */
export function convertGeoJSONToPolygonData(featureCollection: any, isOutline: boolean = false): PolygonData[] {
  const polygonDataList: PolygonData[] = [];

  // 遍历所有 features
  featureCollection?.features.forEach((feature: any, index: number) => {
    const { geometry, properties } = feature;

    // 只处理 Polygon 和 MultiPolygon 类型，跳过 Point 类型
    if (geometry.type === 'Polygon') {
      // Polygon: coordinates[0] 是外轮廓，coordinates[1+] 是内部孔洞
      const outerRing = geometry.coordinates[0];
      const path = convertCoordinatesToPoints(outerRing);

      polygonDataList.push({
        id: `${properties.adcode}-${index}`,
        name: properties.name,
        path,
        properties
      });

    } else if (geometry.type === 'MultiPolygon') {
      // MultiPolygon: 每个 polygon 都是一个独立的形状
      const coordinates = isOutline ? [geometry.coordinates?.[0] ? geometry.coordinates?.[0] : []] : geometry.coordinates;
      coordinates.forEach((polygonCoords: number[][][], polyIndex: number) => {
        // 取每个 polygon 的外轮廓 (polygonCoords[0])
        const outerRing = polygonCoords[0];
        const path = convertCoordinatesToPoints(outerRing);

        polygonDataList.push({
          id: `${properties.adcode}-${index}-${polyIndex}`,
          name: properties.name,
          path,
          properties
        });
      });
    }
    // 跳过 Point 类型，因为 Polygon 组件不需要
  });

  return polygonDataList;
}


// 每隔3位,分割方法
export const onFormatSplit = (num: number) => {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
};



export function getFanShapedSurface(params: FanShapedParams) {
  const { startRatio, endRatio, isSelected, isHovered, k = 0, h } = params;

  // 如果只有一个扇形，则不实现选中效果。
  const selectCondition = isSelected && startRatio !== 0 && endRatio !== 1;
  const midRatio = (startRatio + endRatio) / 2;
  const startRadian = startRatio * Math.PI * 2;
  const endRadian = endRatio * Math.PI * 2;
  const midRadian = midRatio * Math.PI * 2;

  // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
  const offsetX = selectCondition ? Math.cos(midRadian) * 0.1 : 0;
  const offsetY = selectCondition ? Math.sin(midRadian) * 0.1 : 0;

  // 计算高亮效果的放大比例（未高亮，则比例为 1）
  const hoverRate = isHovered ? 1.05 : 1;

  // 返回曲面参数方程
  return {
    u: {
      min: -Math.PI,
      max: Math.PI * 3,
      step: Math.PI / 32,
    },

    v: {
      min: 0,
      max: Math.PI * 2,
      step: Math.PI / 20,
    },

    x(u: number, v: number) {
      if (u < startRadian) {
        return (
          offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate
        );
      }
      if (u > endRadian) {
        return (
          offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate
        );
      }
      return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate;
    },

    y(u: number, v: number) {
      if (u < startRadian) {
        return (
          offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate
        );
      }
      if (u > endRadian) {
        return (
          offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate
        );
      }
      return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate;
    },

    z(u: number, v: number) {
      if (u < -Math.PI * 0.5) {
        return Math.sin(u);
      }
      if (u > Math.PI * 2.5) {
        return Math.sin(u) * h * 0.1;
      }
      // 当前图形的高度是Z根据h（每个value的值决定的）
      return Math.sin(v) > 0 ? 1 * h * 0.1 : -1;
    },
  };
}

// 求百分比 保留两位
export function getPercent(value: number, total: number) {
  return ((value / total) * 100).toFixed(2);
}

// 数字改成万返回 例如 32000返回 3.2
export function getNumberWithWan(value: number) {
  return (value / 10000).toFixed(2);
}